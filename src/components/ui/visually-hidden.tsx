"use client"

import * as React from "react"
import { cn } from "../../lib/utils"

interface VisuallyHiddenProps extends React.ComponentProps<"span"> {
  asChild?: boolean
}

/**
 * VisuallyHidden component that hides content visually while keeping it accessible to screen readers.
 * This is useful for providing context to assistive technologies without cluttering the visual design.
 */
function VisuallyHidden({ 
  className, 
  asChild = false, 
  ...props 
}: VisuallyHiddenProps) {
  const Comp = asChild ? React.Fragment : "span"
  
  return (
    <Comp
      className={cn(
        "sr-only",
        className
      )}
      {...props}
    />
  )
}

export { VisuallyHidden }
