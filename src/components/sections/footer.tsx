'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Linkedin, 
  Twitter, 
  Youtube,
  ExternalLink
} from 'lucide-react';
import { LanguageSwitcher } from '../language-switcher';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
}

function FooterLink({ href, children, external }: FooterLinkProps) {
  return (
    <Link
      href={href}
      className="text-muted-foreground hover:text-primary transition-colors text-sm flex items-center"
      {...(external && { target: "_blank", rel: "noopener noreferrer" })}
    >
      {children}
      {external && <ExternalLink className="w-3 h-3 ml-1" />}
    </Link>
  );
}

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-muted/30 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 sm:py-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-8">
            {/* Company Info */}
            <div className="space-y-4 sm:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-primary-foreground font-bold text-sm">A</span>
                </div>
                <div className="min-w-0">
                  <h3 className="text-lg font-bold text-foreground">AITELMED</h3>
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    GBA-konform | Klasse IIa
                  </Badge>
                </div>
              </div>
              <p className="text-muted-foreground text-sm leading-relaxed">
                medPower® ist eine zertifizierte Telemonitoring-Plattform für die digitale
                Patientenversorgung. GBA-konform, DSGVO-konform und als Medizinprodukt
                Klasse IIa zertifiziert.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <MapPin className="w-4 h-4 flex-shrink-0" />
                  <span>Berlin, Germany</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Phone className="w-4 h-4 flex-shrink-0" />
                  <span>+49 (0) 30 1234 5678</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Mail className="w-4 h-4 flex-shrink-0" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Services */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">Services</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href="#medpower">medPower® Platform</FooterLink>
                <FooterLink href="#telemonitoring">Telemonitoring</FooterLink>
                <FooterLink href="#patient-surveys">Patientenfragebögen</FooterLink>
                <FooterLink href="/configurator">Plattform testen</FooterLink>
                <FooterLink href="#integration">Integration</FooterLink>
              </div>
            </div>

            {/* Support & Resources */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">Support & Ressourcen</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href="/request-demo">Demo anfordern</FooterLink>
                <FooterLink href="/schedule-consultation">Beratung vereinbaren</FooterLink>
                <FooterLink href="#documentation" external>Dokumentation</FooterLink>
                <FooterLink href="#support" external>Technischer Support</FooterLink>
                <FooterLink href="#training">Schulungsressourcen</FooterLink>
                <FooterLink href="#partner-portal" external>Partner Portal</FooterLink>
              </div>
            </div>

            {/* Company & Legal */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">Unternehmen</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href="#about">Über uns</FooterLink>
                <FooterLink href="#careers">Karriere</FooterLink>
                <FooterLink href="#news">News & Presse</FooterLink>
                <FooterLink href="#contact">Kontakt</FooterLink>
                <FooterLink href="#privacy">Datenschutz</FooterLink>
                <FooterLink href="#terms">AGB</FooterLink>
                <FooterLink href="#compliance">Compliance</FooterLink>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Bottom Footer */}
        <div className="py-6 sm:py-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 gap-4">
            {/* Copyright */}
            <div className="text-xs sm:text-sm text-muted-foreground text-center lg:text-left order-3 lg:order-1">
              © {currentYear} AITELMED. Alle Rechte vorbehalten. GBA-konforme Telemedizin-Technologie.
            </div>

            {/* Social Links */}
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 order-1 lg:order-2">
              <span className="text-xs sm:text-sm text-muted-foreground">Follow us:</span>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#linkedin" target="_blank" rel="noopener noreferrer">
                    <Linkedin className="w-4 h-4" />
                    <span className="sr-only">LinkedIn</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#twitter" target="_blank" rel="noopener noreferrer">
                    <Twitter className="w-4 h-4" />
                    <span className="sr-only">Twitter</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#youtube" target="_blank" rel="noopener noreferrer">
                    <Youtube className="w-4 h-4" />
                    <span className="sr-only">YouTube</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Language Selector */}
            <div className="order-2 lg:order-3">
              <LanguageSwitcher variant="select" />
            </div>
          </div>
        </div>

        {/* Final CTA Strip */}
        <div className="py-6 sm:py-8 border-t border-border">
          <div className="text-center space-y-4 sm:space-y-6">
            <h3 className="text-lg sm:text-xl font-semibold text-foreground leading-tight">
              Bereit für die digitale Transformation Ihrer Patientenversorgung?
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Schließen Sie sich hunderten von Arztpraxen und Gesundheitszentren weltweit an,
              die AITELMED für ihre Telemedizin-Bedürfnisse vertrauen.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
              <Button size="lg" className="min-h-[48px]" asChild>
                <Link href="/request-demo">
                  Kostenlose Demo anfordern
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="min-h-[48px]" asChild>
                <Link href="/schedule-consultation">
                  Beratung vereinbaren
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
